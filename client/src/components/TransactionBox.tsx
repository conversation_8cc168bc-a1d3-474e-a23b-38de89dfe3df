import { useState } from "react";
import { Button } from "@/components/ui/button";
import { UserIcon, ArrowUpRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useActiveAccount } from "thirdweb/react";
import { getExplorerUrl } from "@/lib/chainConfig";

interface TransactionData {
  from?: string;
  to?: string;
  value?: string | bigint;
  chainId?: number;
  data?: string;
  gasLimit?: string;
  gasPrice?: string;
  nonce?: number;
}

interface TransactionBoxProps {
  title?: string;
  transactionData: TransactionData;
  onExecute?: (txData: TransactionData) => Promise<string | void>;
  isExecuted?: boolean;
  transactionHash?: string;
  className?: string;
}

const TransactionBox = ({
  title = "Transaction",
  transactionData,
  onExecute,
  isExecuted = false,
  transactionHash,
  className = "",
}: TransactionBoxProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const [isExecuting, setIsExecuting] = useState(false);

  // Helper functions
  const getChainName = (chainId: number) => {
    switch (chainId) {
      case 1:
        return "Ethereum Mainnet";
      case 137:
        return "Polygon Mainnet";
      case 56:
        return "BSC Mainnet";
      case ********:
        return "Sepolia Testnet";
      case 80002:
        return "Amoy Testnet";
      case 97:
        return "BSC Testnet";
      default:
        return `Chain ${chainId}`;
    }
  };

  const getTokenSymbol = (chainId: number) => {
    switch (chainId) {
      case 1:
      case ********:
        return "ETH";
      case 137:
      case 80002:
        return "MATIC";
      case 56:
      case 97:
        return "BNB";
      default:
        return "ETH";
    }
  };

  const formatValue = (value: string | bigint | undefined) => {
    if (!value) return "0";
    const bigIntValue = typeof value === "string" ? BigInt(value) : value;
    return (Number(bigIntValue) / 1e18).toFixed(4);
  };

  const formatAddress = (address: string | undefined) => {
    if (!address) return "N/A";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleExecute = async () => {
    if (!onExecute || isExecuted || isExecuting) return;

    setIsExecuting(true);
    try {
      const result = await onExecute(transactionData);
      if (result) {
        toast({
          title: "Transaction Executed",
          description: "Transaction has been submitted successfully.",
        });
      }
    } catch (error) {
      console.error("Transaction execution failed:", error);
      toast({
        title: "Transaction Failed",
        description: "Failed to execute transaction. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const handleViewOnExplorer = async () => {
    if (!transactionHash || !transactionData.chainId) return;

    try {
      const explorerUrl = await getExplorerUrl(
        transactionData.chainId,
        "tx",
        transactionHash
      );
      window.open(explorerUrl, "_blank");
    } catch (error) {
      console.error("Failed to get explorer URL:", error);
      toast({
        title: "Error",
        description: "Failed to open explorer link.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className={`nebula-transaction-card ${className}`}>
      {/* Header */}
      <div className="text-sm font-medium mb-3">{title}</div>

      {/* From Address */}
      <div className="nebula-transaction-row">
        <div className="nebula-transaction-label">From</div>
        <div className="nebula-transaction-value flex items-center">
          <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
          <span>{formatAddress(transactionData.from)}</span>
        </div>
      </div>

      {/* To Address */}
      <div className="nebula-transaction-row">
        <div className="nebula-transaction-label">To</div>
        <div className="nebula-transaction-value flex items-center">
          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
          <span>{formatAddress(transactionData.to)}</span>
        </div>
      </div>

      {/* Value */}
      <div className="nebula-transaction-row">
        <div className="nebula-transaction-label">Value</div>
        <div className="nebula-transaction-value">
          {formatValue(transactionData.value)}{" "}
          {transactionData.chainId ? getTokenSymbol(transactionData.chainId) : "ETH"}
        </div>
      </div>

      {/* Network */}
      <div className="nebula-transaction-row">
        <div className="nebula-transaction-label">Network</div>
        <div className="nebula-transaction-value flex items-center">
          <ArrowUpRight className="h-4 w-4 mr-2 text-muted-foreground" />
          {transactionData.chainId ? getChainName(transactionData.chainId) : "Unknown"}
        </div>
      </div>

      {/* Action Button */}
      <div className="mt-4 pt-3 border-t border-border/50">
        {transactionHash ? (
          <Button
            className="w-full bg-green-600 hover:bg-green-700 text-white theme-button"
            onClick={handleViewOnExplorer}
          >
            View on Explorer ↗
          </Button>
        ) : (
          <Button
            className="w-full nebula-action-button text-primary-foreground"
            onClick={handleExecute}
            disabled={isExecuting || !onExecute || !activeAccount}
          >
            {isExecuting ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                Executing...
              </>
            ) : (
              <>
                <ArrowUpRight className="h-4 w-4 mr-2" />
                Execute Transaction
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default TransactionBox;
