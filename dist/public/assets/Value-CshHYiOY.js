import{B as f}from"./index-D7hMQ775.js";const s={wei:0,gwei:9,szabo:12,finney:15,ether:18};function c(n,r=0){let t=n.toString();const e=t.startsWith("-");e&&(t=t.slice(1)),t=t.padStart(r,"0");let[o,i]=[t.slice(0,t.length-r),t.slice(t.length-r)];return i=i.replace(/(0+)$/,""),`${e?"-":""}${o||"0"}${i?`.${i}`:""}`}function h(n,r="wei"){return c(n,s.gwei-s[r])}function p(n,r=0){if(!/^(-?)([0-9]*)\.?([0-9]*)$/.test(n))throw new g({value:n});let[t="",e="0"]=n.split(".");const o=t.startsWith("-");if(o&&(t=t.slice(1)),e=e.replace(/(0+)$/,""),r===0)Math.round(+`.${e}`)===1&&(t=`${BigInt(t)+1n}`),e="";else if(e.length>r){const[i,l,u]=[e.slice(0,r-1),e.slice(r-1,r),e.slice(r)],a=Math.round(+`${l}.${u}`);a>9?e=`${BigInt(i)+BigInt(1)}0`.padStart(i.length+1,"0"):e=`${i}${a}`,e.length>r&&(e=e.slice(1),t=`${BigInt(t)+1n}`),e=e.slice(0,r)}else e=e.padEnd(r,"0");return BigInt(`${o?"-":""}${t}${e}`)}class g extends f{constructor({value:r}){super(`Value \`${r}\` is not a valid decimal number.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Value.InvalidDecimalNumberError"})}}export{p as a,c as b,h as f};
