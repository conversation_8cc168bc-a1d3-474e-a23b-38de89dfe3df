const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ownerOf-D8Ohf9Au.js","assets/index-D7hMQ775.js","assets/index-CFzW92q1.css","assets/detectExtension-CvrdXu0d.js"])))=>i.map(i=>d[i]);
import{w as d,ap as u,_ as o,aq as r}from"./index-D7hMQ775.js";import{f as s}from"./fetchTokenMetadata-Kfn5jX3R.js";const I="0xc87b56dd",k=[{type:"uint256",name:"_tokenId"}],l=[{type:"string"}];async function y(t){return d({contract:t.contract,method:[I,k,l],params:[t.tokenId]})}const f="0x4f6ccce7",h=[{type:"uint256",name:"_index"}],_=[{type:"uint256"}];async function m(t){return d({contract:t.contract,method:[f,h,_],params:[t.index]})}async function N(t){const{useIndexer:c=!0}=t;if(c)try{return await T(t)}catch{return await e(t)}return await e(t)}async function T(t){const c=await u({client:t.contract.client,chain:t.contract.chain,contractAddress:t.contract.address,tokenId:t.tokenId,includeOwners:t.includeOwner});return c||e(t)}async function e(t){let c=t.tokenId;if(t.tokenByIndex)try{c=await m({contract:t.contract,index:t.tokenId})}catch{}const[n,a]=await Promise.all([y({contract:t.contract,tokenId:c}).catch(()=>null),t.includeOwner?o(()=>import("./ownerOf-D8Ohf9Au.js"),__vite__mapDeps([0,1,2,3])).then(i=>i.ownerOf({contract:t.contract,tokenId:c})).catch(()=>null):null]);return n!=null&&n.trim()?r(await s({client:t.contract.client,tokenId:c,tokenUri:n}).catch(()=>({id:c,type:"ERC721",uri:n})),{tokenId:c,tokenUri:n,type:"ERC721",owner:a,tokenAddress:t.contract.address,chainId:t.contract.chain.id}):r({id:c,type:"ERC721",uri:""},{tokenId:c,tokenUri:"",type:"ERC721",owner:a,tokenAddress:t.contract.address,chainId:t.contract.chain.id})}export{N as getNFT};
