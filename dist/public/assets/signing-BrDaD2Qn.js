import{by as F,r as P,aJ as C,u as N,bz as U,bA as O,bB as R,bC as L,$ as k,B as z,w,z as g,a3 as j,bD as v,a4 as m,bE as G,aN as W,bF as J,bG as K,bH as S,bI as Z,a7 as b,bJ as T,bK as q,bL as Y,bM as Q,bw as X,bc as x,bf as D}from"./index-D7hMQ775.js";import{concatHex as e0}from"./concat-hex-iLcVZ7cm.js";import{N as t0,e as r0,f as a0}from"./AbiFunction-CgdHaPSO.js";import{a as n0}from"./Signature-DdsVSv8k.js";import{p as I}from"./index-B_tCnAvg.js";import"./approve-DVjCKCNy.js";import"./send-eip712-transaction-CGTUTZ0R.js";import"./eth_sendRawTransaction-DPdnXbFR.js";import"./sha256-CcLOqvRc.js";function s0(e){for(const r of e)if(typeof r!="string")return!1;return!0}function i0(e){return s0(e)?F(e):e}function o0(e,r){var n;const{bytecode:t,args:a}=r;return P(t,(n=e.inputs)!=null&&n.length&&(a!=null&&a.length)?C(e.inputs,a):"0x")}function c0(e){const r=e.find(t=>t.type==="constructor");if(!r)throw new t0({name:"constructor"});return r}function d0(e,r){const t={to:r};switch(t.to){case"number":return b0(e,t);case"bigint":return f0(e,t);case"boolean":return u0(e,t);case"string":return l0(e,t);default:return N(e,t)}}function f0(e,r={}){return U(e,r)}function u0(e,r={}){return O(e,r)}function b0(e,r={}){return R(e,r)}function l0(e,r={}){return L(e,r)}const p0="0x6492649264926492649264926492649264926492649264926492649264926492",y0="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",g0=[{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],stateMutability:"nonpayable",type:"constructor"},{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function",name:"isValidSig"}];function m0(e){if(k(e,-32)!==p0)throw new w0(e)}function h0(e){try{return m0(e),!0}catch{return!1}}class w0 extends z{constructor(r){super(`Value \`${r}\` is an invalid ERC-6492 wrapped signature.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"WrappedSignature.InvalidWrappedSignatureError"})}}const v0="0x1626ba7e",A0=[{type:"bytes32",name:"hash"},{type:"bytes",name:"signature"}],E0=[{type:"bytes4"}];async function S0(e){return w({contract:e.contract,method:[v0,A0,E0],params:[e.hash,e.signature]})}const T0="0x6492649264926492649264926492649264926492649264926492649264926492";function A({address:e,data:r,signature:t}){return e0([g([{type:"address"},{type:"bytes"},{type:"bytes"}],[e,r,t]),T0])}const x0="******************************************";async function V({hash:e,signature:r,address:t,client:a,chain:n,accountFactory:s}){const i=(()=>{if(j(r))return r;if(typeof r=="object"&&"r"in r&&"s"in r)return n0(r);if(r instanceof Uint8Array)return d0(r,"hex");throw new Error(`Invalid signature type for signature ${r}: ${typeof r}`)})();if(await v(m({address:t,client:a,chain:n}))&&await h({hash:e,signature:i,contract:m({chain:n,address:t,client:a})}).catch(u=>(console.error("Error verifying EIP-1271 signature",u),!1)))return!0;const y=await(async()=>!s||h0(i)?i:A({address:s.address,data:s.verificationCalldata,signature:i}))();let d;const c=await G(n),l=i0(g0);if(c)d={to:x0,data:r0(a0(l,"isValidSig"),[t,e,y])};else{const f=c0(l);d={data:o0(f,{args:[t,e,y],bytecode:y0})}}const p=W({chain:n,client:a});try{const f=await J(p,d);return K(f)}catch{return!!await h({hash:e,signature:i,contract:m({chain:n,address:t,client:a})}).catch(u=>(console.error("Error verifying EIP-1271 signature",u),!1))}}const D0="0x1626ba7e";async function h({hash:e,signature:r,contract:t}){try{return await S0({hash:e,signature:r,contract:t})===D0}catch(a){return console.error("Error verifying EIP-1271 signature",a),!1}}const I0=`Ethereum Signed Message:
`;function V0(e,r){const t=typeof e=="string"?S(e):e.raw instanceof Uint8Array?e.raw:Z(e.raw),a=S(`${I0}${t.length}`);return b(T(a,t),r)}function B0(e){const{domain:r={},message:t,primaryType:a}=e,n={EIP712Domain:q(r),...e.types};Y({domain:r,message:t,primaryType:a,types:n});const s=["0x1901"];if(r&&s.push(Q({domain:r,types:n})),a!=="EIP712Domain"){const i=(()=>{const o=B({data:t,primaryType:a,types:n});return b(o)})();s.push(i)}return b(T(...s.map(i=>X(i))))}function B({data:e,primaryType:r,types:t}){const a=[{type:"bytes32"}],n=[M0({primaryType:r,types:t})];if(!t[r])throw new Error("Invalid types");for(const s of t[r]){const[i,o]=_({types:t,name:s.name,type:s.type,value:e[s.name]});a.push(i),n.push(o)}return g(a,n)}function M0({primaryType:e,types:r}){const t=x(_0({primaryType:e,types:r}));return b(t)}function _0({primaryType:e,types:r}){let t="";const a=M({primaryType:e,types:r});a.delete(e);const n=[e,...Array.from(a).sort()];for(const s of n){if(!r[s])throw new Error("Invalid types");t+=`${s}(${r[s].map(({name:i,type:o})=>`${o} ${i}`).join(",")})`}return t}function M({primaryType:e,types:r},t=new Set){const a=e.match(/^\w*/u),n=a==null?void 0:a[0];if(t.has(n)||r[n]===void 0)return t;t.add(n);for(const s of r[n])M({primaryType:s.type,types:r},t);return t}function _({types:e,name:r,type:t,value:a}){if(e[t]!==void 0)return[{type:"bytes32"},b(B({data:a,primaryType:t,types:e}))];if(t==="bytes")return a=`0x${(a.length%2?"0":"")+a.slice(2)}`,[{type:"bytes32"},b(a)];if(t==="string")return[{type:"bytes32"},b(x(a))];if(t.lastIndexOf("]")===t.length-1){const n=t.slice(0,t.lastIndexOf("[")),s=a.map(i=>_({name:r,type:n,types:e,value:i}));return[{type:"bytes32"},b(g(s.map(([i])=>i),s.map(([,i])=>i)))]}return[{type:t},a]}async function k0({accountContract:e,factoryContract:r,options:t,message:a}){var y,d;const n=V0(a),s=await $({factoryContract:r,accountContract:e,originalMsgHash:n});let i;if(s){const c=g([{type:"bytes32"}],[n]);i=await t.personalAccount.signTypedData({domain:{name:"Account",version:"1",chainId:t.chain.id,verifyingContract:e.address},primaryType:"AccountMessage",types:{AccountMessage:[{name:"message",type:"bytes"}]},message:{message:c}})}else i=await t.personalAccount.signMessage({message:a});if(await v(e)){if(await h({hash:n,signature:i,contract:e}))return i;throw new Error("Failed to verify signature")}else{const c=I({factoryContract:r,adminAddress:t.personalAccount.address,accountSalt:(y=t.overrides)==null?void 0:y.accountSalt,createAccountOverride:(d=t.overrides)==null?void 0:d.createAccount});if(!c)throw new Error("Create account override not provided");const l=await D(c),p=A({address:r.address,data:l,signature:i});if(await V({hash:n,signature:p,address:e.address,chain:e.chain,client:e.client}))return p;throw new Error("Unable to verify ERC-6492 signature after signing.")}}async function z0({accountContract:e,factoryContract:r,options:t,typedData:a}){var d,c,l,p,f;if(((c=(d=a.domain)==null?void 0:d.verifyingContract)==null?void 0:c.toLowerCase())===((l=e.address)==null?void 0:l.toLowerCase()))return t.personalAccount.signTypedData(a);const s=B0(a),i=await $({factoryContract:r,accountContract:e,originalMsgHash:s});let o;if(i){const u=g([{type:"bytes32"}],[s]);o=await t.personalAccount.signTypedData({domain:{name:"Account",version:"1",chainId:t.chain.id,verifyingContract:e.address},primaryType:"AccountMessage",types:{AccountMessage:[{name:"message",type:"bytes"}]},message:{message:u}})}else o=await t.personalAccount.signTypedData(a);if(await v(e)){if(await h({hash:s,signature:o,contract:e}))return o;throw new Error("Failed to verify signature")}else{const u=I({factoryContract:r,adminAddress:t.personalAccount.address,accountSalt:(p=t.overrides)==null?void 0:p.accountSalt,createAccountOverride:(f=t.overrides)==null?void 0:f.createAccount});if(!u)throw new Error("Create account override not provided");const H=await D(u),E=A({address:r.address,data:H,signature:o});if(await V({hash:s,signature:E,address:e.address,chain:e.chain,client:e.client}))return E;throw new Error("Unable to verify signature on smart account, please make sure the admin wallet has permissions and the signature is valid.")}}async function $({factoryContract:e,accountContract:r,originalMsgHash:t}){try{const a=await w({contract:e,method:"function accountImplementation() public view returns (address)"});return await w({contract:m({address:a,chain:r.chain,client:r.client}),method:"function getMessageHash(bytes32 _hash) public view returns (bytes32)",params:[t]}).then(s=>s!=="0x").catch(()=>!1)}catch{return!1}}export{k0 as smartAccountSignMessage,z0 as smartAccountSignTypedData};
